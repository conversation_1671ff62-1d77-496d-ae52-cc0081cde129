#!/usr/bin/env python3
"""
修复产品表结构脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file

load_env_file()

from utils.database import get_database_manager
from utils.logging_config import get_logger

logger = get_logger()


def create_products_complete_table():
    """创建或修复 products_complete 表"""
    db_manager = get_database_manager()

    try:
        conn = db_manager.get_connection_with_fallback()

        try:
            with conn.cursor() as cursor:
                print("🔧 创建/修复 products_complete 表...")

                # 创建表的SQL
                create_table_sql = """
                CREATE TABLE IF NOT EXISTS products_complete (
                    id SERIAL PRIMARY KEY,
                    product_id VARCHAR(100) UNIQUE NOT NULL,
                    name TEXT,
                    spec TEXT,
                    introduction TEXT,
                    extracted_model TEXT,
                    category_id INTEGER,
                    category_name VARCHAR(255),
                    brand_id INTEGER,
                    brand_name VARCHAR(255),
                    price DECIMAL(10,2) DEFAULT 0.0,
                    all_fields JSONB,
                    unknown_fields JSONB,
                    parsed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """

                cursor.execute(create_table_sql)

                # 创建索引
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_products_complete_product_id ON products_complete(product_id)",
                    "CREATE INDEX IF NOT EXISTS idx_products_complete_category_id ON products_complete(category_id)",
                    "CREATE INDEX IF NOT EXISTS idx_products_complete_brand_id ON products_complete(brand_id)",
                    "CREATE INDEX IF NOT EXISTS idx_products_complete_name ON products_complete(name)",
                    "CREATE INDEX IF NOT EXISTS idx_products_complete_updated_at ON products_complete(updated_at)",
                ]

                for index_sql in indexes:
                    cursor.execute(index_sql)

                conn.commit()
                print("✅ products_complete 表创建/修复成功")

                # 验证表结构
                cursor.execute(
                    """
                    SELECT column_name, data_type
                    FROM information_schema.columns
                    WHERE table_name = 'products_complete'
                    ORDER BY ordinal_position
                """
                )

                columns = cursor.fetchall()
                print(f"\n📋 表结构验证 ({len(columns)} 个字段):")

                # 检查关键字段
                column_names = [col[0] for col in columns]
                key_fields = [
                    "product_id",
                    "name",
                    "extracted_model",
                    "category_id",
                    "brand_id",
                ]

                for field in key_fields:
                    if field in column_names:
                        print(f"  ✅ {field}")
                    else:
                        print(f"  ❌ {field} - 缺失")

                # 检查product_id字段类型
                cursor.execute(
                    """
                    SELECT data_type
                    FROM information_schema.columns
                    WHERE table_name = 'products_complete' AND column_name = 'product_id'
                """
                )

                result = cursor.fetchone()
                if result:
                    print(f"\n📊 product_id 字段类型: {result[0]}")
                else:
                    print("\n❌ product_id 字段不存在")

                return True

        finally:
            db_manager.return_connection(conn)

    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        logger.error(f"创建表失败: {e}", exc_info=True)
        return False


def test_product_storage():
    """测试产品存储功能"""
    print("\n🧪 测试产品存储功能...")

    try:
        from services.product_storage_service import ProductStorageService

        storage_service = ProductStorageService()

        # 准备测试数据 - 使用字符串ID
        test_product = {
            "productId": "test_string_id_001",
            "productName": "测试产品",
            "brandName": "测试品牌",
            "categoryName": "测试分类",
            "price": 100.0,
            "paramInfoList": [
                {"params": "型号", "content": "TEST-001"},
                {"params": "规格", "content": "测试规格"},
            ],
        }

        print(f"测试数据: {test_product}")

        # 执行存储
        result = storage_service.store_products_batch([test_product])
        print(f"存储结果: {result}")

        if result.get("errors", 0) == 0:
            print("✅ 产品存储测试成功")
            return True
        else:
            print("❌ 产品存储测试失败")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)
        return False


def main():
    """主函数"""
    print("🚀 修复产品表结构")
    print("=" * 50)

    # 1. 创建/修复表结构
    if not create_products_complete_table():
        print("❌ 表结构修复失败")
        return False

    # 2. 测试产品存储
    if not test_product_storage():
        print("❌ 产品存储测试失败")
        return False

    print("\n🎉 产品表结构修复完成！")
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        logger.error(f"脚本执行失败: {e}", exc_info=True)
        sys.exit(1)
