#!/usr/bin/env python3
"""
API认证安全优化脚本

清理代码中的硬编码凭据，强制使用环境变量，提高系统安全性
"""

import os
import re
import shutil
from datetime import datetime
from typing import List, Dict, Any, Tuple
from pathlib import Path

from utils.logging_config import get_logger

logger = get_logger()


class SecurityOptimizer:
    """安全优化器"""

    def __init__(self):
        self.project_root = Path(".")
        self.backup_dir = Path("security_backup")
        self.security_issues = []

    def scan_security_issues(self) -> List[Dict[str, Any]]:
        """扫描安全问题"""
        logger.info("🔍 扫描代码中的安全问题...")

        issues = []

        # 定义需要检查的文件模式
        file_patterns = ["*.py", "*.bat", "*.ps1", "*.md", "*.txt", "*.env*"]

        # 定义敏感信息模式
        sensitive_patterns = [
            (r'password["\s]*[:=]["\s]*[^"\s\n]+', "硬编码密码"),
            (r'ZKMALL_PASSWORD["\s]*[:=]["\s]*[^"\s\n]+', "API密码硬编码"),
            (r"Zk@123456", "明文密码"),
            (r"18929343717", "硬编码用户名"),
            (r"username:password@", "数据库连接字符串中的明文密码"),
            (r'api_key["\s]*[:=]["\s]*[^"\s\n]+', "API密钥硬编码"),
            (r'secret["\s]*[:=]["\s]*[^"\s\n]+', "密钥硬编码"),
            (r'token["\s]*[:=]["\s]*[^"\s\n]+', "令牌硬编码"),
        ]

        for pattern in file_patterns:
            try:
                for file_path in self.project_root.rglob(pattern):
                    # 跳过虚拟环境和备份目录
                    if any(
                        part in str(file_path)
                        for part in ["venv", "__pycache__", ".git", "security_backup"]
                    ):
                        continue

                    # 跳过无法访问的文件
                    if not file_path.exists() or not os.access(file_path, os.R_OK):
                        continue

                    try:
                        with open(
                            file_path, "r", encoding="utf-8", errors="ignore"
                        ) as f:
                            content = f.read()

                        for pattern_regex, issue_type in sensitive_patterns:
                            matches = re.finditer(pattern_regex, content, re.IGNORECASE)
                            for match in matches:
                                line_num = content[: match.start()].count("\n") + 1
                                issues.append(
                                    {
                                        "file": str(file_path),
                                        "line": line_num,
                                        "type": issue_type,
                                        "content": match.group().strip(),
                                        "severity": (
                                            "HIGH"
                                            if "password" in issue_type.lower()
                                            else "MEDIUM"
                                        ),
                                    }
                                )

                    except Exception as e:
                        logger.warning(f"无法读取文件 {file_path}: {e}")

            except (OSError, PermissionError) as e:
                logger.warning(f"跳过无法访问的目录模式 {pattern}: {e}")
                continue

        self.security_issues = issues
        logger.info(f"发现 {len(issues)} 个安全问题")
        return issues

    def create_backup(self) -> bool:
        """创建备份"""
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)

            self.backup_dir.mkdir()

            # 备份关键文件
            critical_files = [
                "config.py",
                "set_env.bat",
                "set_env.ps1",
                "utils/api_client.py",
            ]

            for file_path in critical_files:
                if Path(file_path).exists():
                    backup_path = self.backup_dir / Path(file_path).name
                    shutil.copy2(file_path, backup_path)
                    logger.info(f"备份文件: {file_path} -> {backup_path}")

            return True

        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False

    def fix_hardcoded_credentials(self) -> bool:
        """修复硬编码凭据"""
        logger.info("🔧 修复硬编码凭据...")

        try:
            # 1. 修复 set_env.bat
            self._fix_set_env_bat()

            # 2. 修复 set_env.ps1
            self._fix_set_env_ps1()

            # 3. 修复 config.py
            self._fix_config_py()

            # 4. 创建安全的环境变量模板
            self._create_secure_env_template()

            # 5. 创建安全配置指南
            self._create_security_guide()

            return True

        except Exception as e:
            logger.error(f"修复硬编码凭据失败: {e}")
            return False

    def _fix_set_env_bat(self):
        """修复 set_env.bat"""
        bat_file = Path("set_env.bat")
        if not bat_file.exists():
            return

        logger.info("修复 set_env.bat 中的硬编码凭据")

        secure_content = """@echo off
REM Windows batch script for setting environment variables
REM Security-enhanced version - NO hardcoded credentials

echo Setting up environment variables for YunShang System...

REM Database Configuration
set DATABASE_HOST=***********
set DATABASE_PORT=5432
set DATABASE_NAME=product
set DATABASE_USER=username

REM SECURITY WARNING: Set these sensitive variables manually or use .env file
REM set DATABASE_PASSWORD=your_secure_password_here

REM API Configuration
set ZKMALL_API_BASE=https://zkmall.zktecoiot.com

REM SECURITY WARNING: Set these sensitive variables manually or use .env file
REM set ZKMALL_USERNAME=your_username_here
REM set ZKMALL_PASSWORD=your_secure_password_here

REM Application Configuration
set LOG_LEVEL=INFO
set DEBUG=False
set CACHE_TTL=3600

echo.
echo SECURITY NOTICE:
echo Please set the following environment variables manually:
echo - DATABASE_PASSWORD
echo - ZKMALL_USERNAME  
echo - ZKMALL_PASSWORD
echo.
echo Or create a .env file with these values.
echo.

pause
"""

        with open(bat_file, "w", encoding="utf-8") as f:
            f.write(secure_content)

        logger.info("✅ set_env.bat 安全优化完成")

    def _fix_set_env_ps1(self):
        """修复 set_env.ps1"""
        ps1_file = Path("set_env.ps1")
        if not ps1_file.exists():
            return

        logger.info("修复 set_env.ps1 中的硬编码凭据")

        secure_content = """# PowerShell script for setting environment variables
# Security-enhanced version - NO hardcoded credentials

Write-Host "Setting up environment variables for YunShang System..." -ForegroundColor Green

# Database Configuration
$env:DATABASE_HOST = "***********"
$env:DATABASE_PORT = "5432"
$env:DATABASE_NAME = "product"
$env:DATABASE_USER = "username"

# SECURITY WARNING: Set these sensitive variables manually or use .env file
# $env:DATABASE_PASSWORD = "your_secure_password_here"

# API Configuration
$env:ZKMALL_API_BASE = "https://zkmall.zktecoiot.com"

# SECURITY WARNING: Set these sensitive variables manually or use .env file
# $env:ZKMALL_USERNAME = "your_username_here"
# $env:ZKMALL_PASSWORD = "your_secure_password_here"

# Application Configuration
$env:LOG_LEVEL = "INFO"
$env:DEBUG = "False"
$env:CACHE_TTL = "3600"

Write-Host ""
Write-Host "SECURITY NOTICE:" -ForegroundColor Yellow
Write-Host "Please set the following environment variables manually:" -ForegroundColor Yellow
Write-Host "- DATABASE_PASSWORD" -ForegroundColor Red
Write-Host "- ZKMALL_USERNAME" -ForegroundColor Red
Write-Host "- ZKMALL_PASSWORD" -ForegroundColor Red
Write-Host ""
Write-Host "Or create a .env file with these values." -ForegroundColor Yellow
Write-Host ""

Read-Host "Press Enter to continue"
"""

        with open(ps1_file, "w", encoding="utf-8") as f:
            f.write(secure_content)

        logger.info("✅ set_env.ps1 安全优化完成")

    def _fix_config_py(self):
        """修复 config.py 中的默认值"""
        config_file = Path("config.py")
        if not config_file.exists():
            return

        logger.info("修复 config.py 中的硬编码凭据")

        with open(config_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 替换硬编码的用户名和密码
        content = re.sub(
            r'"username": os\.getenv\("ZKMALL_USERNAME", "[^"]*"\)',
            '"username": os.getenv("ZKMALL_USERNAME")',
            content,
        )

        content = re.sub(
            r'"password": os\.getenv\("ZKMALL_PASSWORD", "[^"]*"\)',
            '"password": os.getenv("ZKMALL_PASSWORD")',
            content,
        )

        # 添加安全验证
        if "validate_config" in content:
            # 增强验证逻辑
            enhanced_validation = """
        # 验证API配置
        if not self.API_CONFIG["username"]:
            errors.append("API用户名未设置 (ZKMALL_USERNAME)")
            
        if not self.API_CONFIG["password"]:
            errors.append("API密码未设置 (ZKMALL_PASSWORD)")
            
        # 安全检查：确保没有使用默认或测试凭据
        if self.API_CONFIG["username"] in ["test", "admin", "18929343717"]:
            errors.append("检测到不安全的API用户名，请使用生产环境凭据")
            
        if self.API_CONFIG["password"] in ["test", "password", "123456", "Zk@123456"]:
            errors.append("检测到不安全的API密码，请使用强密码")
"""

            content = re.sub(
                r'# 验证API配置\s*if not self\.API_CONFIG\["password"\]:[^}]+',
                enhanced_validation.strip(),
                content,
                flags=re.DOTALL,
            )

        with open(config_file, "w", encoding="utf-8") as f:
            f.write(content)

        logger.info("✅ config.py 安全优化完成")

    def _create_secure_env_template(self):
        """创建安全的环境变量模板"""
        template_file = Path(".env.template")

        template_content = """# 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# 数据库配置
DB_HOST=***********
DB_PORT=5432
DB_NAME=product
DB_USER=username
DB_PASSWORD=your_secure_database_password_here

# API配置
ZKMALL_API_BASE=https://zkmall.zktecoiot.com
ZKMALL_USERNAME=your_api_username_here
ZKMALL_PASSWORD=your_secure_api_password_here
API_TIMEOUT=30
API_RETRY_COUNT=3

# FastGPT配置（可选）
FASTGPT_API_BASE=https://api.fastgpt.in
FASTGPT_API_KEY=your_fastgpt_api_key_here
FASTGPT_TIMEOUT=30
FASTGPT_MAX_RETRIES=3

# 系统配置
LOG_LEVEL=INFO
DEBUG=False
ENVIRONMENT=production
TIMEZONE=Asia/Shanghai

# 安全提示：
# 1. 请使用强密码
# 2. 不要将 .env 文件提交到版本控制系统
# 3. 定期更换密码
# 4. 使用最小权限原则
"""

        with open(template_file, "w", encoding="utf-8") as f:
            f.write(template_content)

        logger.info("✅ 创建安全环境变量模板: .env.template")

    def _create_security_guide(self):
        """创建安全配置指南"""
        guide_file = Path("SECURITY_GUIDE.md")

        guide_content = f"""# 安全配置指南

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 🔒 安全优化完成

本系统已完成安全优化，移除了所有硬编码凭据。

## 📋 安全检查清单

### ✅ 已完成的安全优化

1. **移除硬编码凭据**
   - 清理了 `set_env.bat` 中的明文密码
   - 清理了 `set_env.ps1` 中的明文密码
   - 优化了 `config.py` 中的默认值
   - 增强了配置验证逻辑

2. **创建安全模板**
   - 生成了 `.env.template` 模板文件
   - 提供了安全配置示例

3. **增强验证机制**
   - 添加了弱密码检测
   - 强制要求环境变量配置

## 🚀 部署步骤

### 1. 配置环境变量

**方法一：使用 .env 文件（推荐）**
```bash
# 复制模板文件
cp .env.template .env

# 编辑 .env 文件，填入实际凭据
# 注意：不要将 .env 文件提交到版本控制系统
```

**方法二：手动设置环境变量**
```powershell
# Windows PowerShell
$env:ZKMALL_USERNAME = "your_actual_username"
$env:ZKMALL_PASSWORD = "your_secure_password"
$env:DB_PASSWORD = "your_database_password"
```

### 2. 验证配置

运行配置验证脚本：
```bash
python -c "from config import config; print('配置验证:', config.validate_config())"
```

### 3. 安全最佳实践

1. **密码安全**
   - 使用强密码（至少12位，包含大小写字母、数字、特殊字符）
   - 定期更换密码
   - 不要在代码中硬编码密码

2. **环境变量管理**
   - 使用 `.env` 文件管理敏感配置
   - 将 `.env` 添加到 `.gitignore`
   - 生产环境使用系统环境变量

3. **访问控制**
   - 使用最小权限原则
   - 定期审查访问权限
   - 监控异常访问

## ⚠️ 安全警告

1. **不要在代码中硬编码凭据**
2. **不要将 .env 文件提交到版本控制系统**
3. **定期更换密码和API密钥**
4. **监控系统访问日志**

## 🔍 安全检查命令

```bash
# 检查是否还有硬编码凭据
python security_optimization.py --scan

# 验证当前配置安全性
python security_optimization.py --validate
```

## 📞 支持

如有安全相关问题，请联系系统管理员。
"""

        with open(guide_file, "w", encoding="utf-8") as f:
            f.write(guide_content)

        logger.info("✅ 创建安全配置指南: SECURITY_GUIDE.md")

    def generate_security_report(self) -> str:
        """生成安全报告"""
        report = f"""
# 安全优化报告

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 发现的安全问题
总计: {len(self.security_issues)} 个

### 按严重程度分类
"""

        high_issues = [i for i in self.security_issues if i["severity"] == "HIGH"]
        medium_issues = [i for i in self.security_issues if i["severity"] == "MEDIUM"]

        report += f"- 高危: {len(high_issues)} 个\n"
        report += f"- 中危: {len(medium_issues)} 个\n\n"

        if high_issues:
            report += "### 高危问题详情\n"
            for issue in high_issues[:10]:  # 只显示前10个
                report += f"- **{issue['file']}:{issue['line']}** - {issue['type']}\n"
                report += f"  内容: `{issue['content'][:50]}...`\n\n"

        report += """
## 已应用的修复措施

1. ✅ 移除 set_env.bat 中的硬编码密码
2. ✅ 移除 set_env.ps1 中的硬编码密码  
3. ✅ 优化 config.py 中的默认值
4. ✅ 创建安全的环境变量模板
5. ✅ 增强配置验证逻辑
6. ✅ 创建安全配置指南

## 建议的后续行动

1. 🔄 更新所有生产环境的凭据
2. 🔍 定期进行安全扫描
3. 📚 培训开发团队安全最佳实践
4. 🔐 实施密码轮换策略
"""

        return report

    def run_optimization(self) -> bool:
        """运行完整的安全优化"""
        logger.info("🚀 开始API认证安全优化...")

        try:
            # 1. 扫描安全问题
            self.scan_security_issues()

            # 2. 创建备份
            if not self.create_backup():
                logger.error("备份失败，停止优化")
                return False

            # 3. 修复硬编码凭据
            if not self.fix_hardcoded_credentials():
                logger.error("修复失败")
                return False

            # 4. 生成安全报告
            report = self.generate_security_report()
            with open("SECURITY_REPORT.md", "w", encoding="utf-8") as f:
                f.write(report)

            logger.info("✅ API认证安全优化完成")
            logger.info("📋 请查看 SECURITY_GUIDE.md 了解部署步骤")
            logger.info("📊 详细报告请查看 SECURITY_REPORT.md")

            return True

        except Exception as e:
            logger.error(f"安全优化失败: {e}", exc_info=True)
            return False


def main():
    """主函数"""
    import sys

    optimizer = SecurityOptimizer()

    if len(sys.argv) > 1:
        if sys.argv[1] == "--scan":
            issues = optimizer.scan_security_issues()
            print(f"发现 {len(issues)} 个安全问题")
            for issue in issues[:10]:
                print(f"- {issue['file']}:{issue['line']} - {issue['type']}")
            return
        elif sys.argv[1] == "--validate":
            from config import config

            is_valid = config.validate_config()
            print(f"配置验证结果: {'通过' if is_valid else '失败'}")
            return

    # 运行完整优化
    success = optimizer.run_optimization()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
