#!/usr/bin/env python3
"""
优化的批量数据处理服务

基于性能测试结果优化的批量处理器，提供：
1. 最优批量大小处理（100条记录）
2. 智能连接池管理
3. 并发处理支持
4. 错误恢复机制
5. 性能监控
"""

import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import queue
import logging

from utils.database import get_database_manager
from services.product_storage_service import ProductStorageService
from utils.cache import CacheManager
from utils.logging_config import get_logger

logger = get_logger()


@dataclass
class BatchProcessingConfig:
    """批量处理配置"""
    optimal_batch_size: int = 100
    max_concurrent_threads: int = 5
    max_retries: int = 3
    retry_delay: float = 1.0
    connection_timeout: float = 30.0
    enable_performance_monitoring: bool = True


@dataclass
class ProcessingResult:
    """处理结果"""
    total_items: int
    processed_items: int
    failed_items: int
    processing_time: float
    throughput: float
    errors: List[str]
    batch_results: List[Dict[str, Any]]


class OptimizedBatchProcessor:
    """优化的批量处理器"""
    
    def __init__(self, config: Optional[BatchProcessingConfig] = None):
        self.config = config or BatchProcessingConfig()
        self.db_manager = get_database_manager()
        self.storage_service = ProductStorageService()
        self.cache_manager = CacheManager()
        
        # 性能监控
        self.performance_stats = {
            "total_processed": 0,
            "total_errors": 0,
            "avg_throughput": 0.0,
            "last_processing_time": None
        }
        
        # 线程安全锁
        self._stats_lock = threading.Lock()
        
        logger.info(f"优化批量处理器初始化完成 - 批量大小: {self.config.optimal_batch_size}, 并发线程: {self.config.max_concurrent_threads}")
    
    def process_products_batch(self, products: List[Dict[str, Any]], 
                             progress_callback: Optional[Callable[[int, int], None]] = None) -> ProcessingResult:
        """
        批量处理产品数据
        
        Args:
            products: 产品数据列表
            progress_callback: 进度回调函数 (processed, total)
            
        Returns:
            ProcessingResult: 处理结果
        """
        start_time = time.time()
        total_items = len(products)
        
        logger.info(f"开始批量处理 {total_items} 条产品数据")
        
        # 分割成最优批量大小
        batches = self._split_into_batches(products, self.config.optimal_batch_size)
        
        # 并发处理批次
        batch_results = []
        processed_items = 0
        failed_items = 0
        errors = []
        
        if len(batches) <= self.config.max_concurrent_threads:
            # 小批量数据，使用并发处理
            batch_results, processed_items, failed_items, errors = self._process_concurrent_batches(
                batches, progress_callback, total_items
            )
        else:
            # 大批量数据，使用串行处理避免资源耗尽
            batch_results, processed_items, failed_items, errors = self._process_sequential_batches(
                batches, progress_callback, total_items
            )
        
        processing_time = time.time() - start_time
        throughput = processed_items / processing_time if processing_time > 0 else 0
        
        # 更新性能统计
        self._update_performance_stats(processed_items, failed_items, throughput, processing_time)
        
        result = ProcessingResult(
            total_items=total_items,
            processed_items=processed_items,
            failed_items=failed_items,
            processing_time=processing_time,
            throughput=throughput,
            errors=errors,
            batch_results=batch_results
        )
        
        logger.info(f"批量处理完成 - 总计: {total_items}, 成功: {processed_items}, 失败: {failed_items}, "
                   f"耗时: {processing_time:.2f}s, 吞吐量: {throughput:.2f} 记录/秒")
        
        return result
    
    def _split_into_batches(self, items: List[Any], batch_size: int) -> List[List[Any]]:
        """将数据分割成批次"""
        batches = []
        for i in range(0, len(items), batch_size):
            batches.append(items[i:i + batch_size])
        return batches
    
    def _process_concurrent_batches(self, batches: List[List[Dict[str, Any]]], 
                                  progress_callback: Optional[Callable[[int, int], None]], 
                                  total_items: int) -> tuple:
        """并发处理批次"""
        batch_results = []
        processed_items = 0
        failed_items = 0
        errors = []
        
        with ThreadPoolExecutor(max_workers=self.config.max_concurrent_threads) as executor:
            # 提交所有批次任务
            future_to_batch = {
                executor.submit(self._process_single_batch_with_retry, i, batch): (i, batch)
                for i, batch in enumerate(batches)
            }
            
            # 收集结果
            for future in as_completed(future_to_batch):
                batch_index, batch = future_to_batch[future]
                try:
                    result = future.result()
                    batch_results.append(result)
                    
                    processed_items += result.get("inserted", 0) + result.get("updated", 0)
                    failed_items += result.get("errors", 0)
                    
                    if result.get("error_messages"):
                        errors.extend(result["error_messages"])
                    
                    # 调用进度回调
                    if progress_callback:
                        progress_callback(processed_items, total_items)
                        
                except Exception as e:
                    error_msg = f"批次 {batch_index} 处理失败: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    failed_items += len(batch)
        
        return batch_results, processed_items, failed_items, errors
    
    def _process_sequential_batches(self, batches: List[List[Dict[str, Any]]], 
                                  progress_callback: Optional[Callable[[int, int], None]], 
                                  total_items: int) -> tuple:
        """串行处理批次"""
        batch_results = []
        processed_items = 0
        failed_items = 0
        errors = []
        
        for i, batch in enumerate(batches):
            try:
                result = self._process_single_batch_with_retry(i, batch)
                batch_results.append(result)
                
                processed_items += result.get("inserted", 0) + result.get("updated", 0)
                failed_items += result.get("errors", 0)
                
                if result.get("error_messages"):
                    errors.extend(result["error_messages"])
                
                # 调用进度回调
                if progress_callback:
                    progress_callback(processed_items, total_items)
                    
                logger.debug(f"批次 {i+1}/{len(batches)} 完成")
                
            except Exception as e:
                error_msg = f"批次 {i} 处理失败: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
                failed_items += len(batch)
        
        return batch_results, processed_items, failed_items, errors
    
    def _process_single_batch_with_retry(self, batch_index: int, batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """带重试的单批次处理"""
        last_error = None
        
        for attempt in range(self.config.max_retries):
            try:
                logger.debug(f"处理批次 {batch_index} (尝试 {attempt + 1}/{self.config.max_retries})")
                result = self.storage_service.store_products_batch(batch)
                
                # 添加批次信息
                result["batch_index"] = batch_index
                result["batch_size"] = len(batch)
                result["attempt"] = attempt + 1
                
                return result
                
            except Exception as e:
                last_error = e
                logger.warning(f"批次 {batch_index} 第 {attempt + 1} 次尝试失败: {str(e)}")
                
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))  # 指数退避
        
        # 所有重试都失败
        error_msg = f"批次 {batch_index} 所有重试都失败: {str(last_error)}"
        logger.error(error_msg)
        
        return {
            "batch_index": batch_index,
            "batch_size": len(batch),
            "inserted": 0,
            "updated": 0,
            "errors": len(batch),
            "total": len(batch),
            "error_messages": [error_msg],
            "failed": True
        }
    
    def _update_performance_stats(self, processed: int, failed: int, throughput: float, processing_time: float):
        """更新性能统计"""
        with self._stats_lock:
            self.performance_stats["total_processed"] += processed
            self.performance_stats["total_errors"] += failed
            
            # 计算平均吞吐量
            if self.performance_stats["avg_throughput"] == 0:
                self.performance_stats["avg_throughput"] = throughput
            else:
                self.performance_stats["avg_throughput"] = (
                    self.performance_stats["avg_throughput"] + throughput
                ) / 2
            
            self.performance_stats["last_processing_time"] = processing_time
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        with self._stats_lock:
            return self.performance_stats.copy()
    
    def reset_performance_stats(self):
        """重置性能统计"""
        with self._stats_lock:
            self.performance_stats = {
                "total_processed": 0,
                "total_errors": 0,
                "avg_throughput": 0.0,
                "last_processing_time": None
            }
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }
        
        try:
            # 检查数据库连接
            pool_status = self.db_manager.get_pool_status()
            health_status["components"]["database"] = {
                "status": "healthy",
                "pool_status": pool_status
            }
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["components"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        try:
            # 检查缓存系统
            test_key = f"health_check_{int(time.time())}"
            self.cache_manager.set(test_key, "test", ttl=10)
            cached_value = self.cache_manager.get(test_key)
            self.cache_manager.delete(test_key)
            
            health_status["components"]["cache"] = {
                "status": "healthy" if cached_value == "test" else "degraded"
            }
        except Exception as e:
            health_status["components"]["cache"] = {
                "status": "degraded",
                "error": str(e)
            }
        
        # 添加性能统计
        health_status["performance"] = self.get_performance_stats()
        
        return health_status


# 全局实例
_batch_processor_instance = None


def get_batch_processor() -> OptimizedBatchProcessor:
    """获取批量处理器实例（单例模式）"""
    global _batch_processor_instance
    if _batch_processor_instance is None:
        _batch_processor_instance = OptimizedBatchProcessor()
    return _batch_processor_instance
