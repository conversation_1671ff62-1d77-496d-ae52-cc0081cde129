import requests
import logging
import json
from typing import Dict, List, Any, Optional, Union
import os
import time
from utils.auth import AuthManager
from .api_response_normalizer import ApiResponseNormalizer
from .enhanced_cache import cache_query, get_cache_manager, invalidate_cache

logger = logging.getLogger(__name__)


class ZKMallClient:
    """熵基云商API客户端"""

    def __init__(self, base_url: Optional[str] = None):
        """
        初始化API客户端

        Args:
            base_url: API基础URL
        """
        self.base_url = base_url or os.getenv(
            "ZKMALL_API_BASE", "https://zkmall.zktecoiot.com"
        )
        self.session = requests.Session()

        # 设置默认请求头
        self.session.headers.update(
            {"Content-Type": "application/json", "User-Agent": "YunShang-System/1.0"}
        )

        # 设置超时
        self.timeout = 30

        # 重试配置
        self.max_retries = 3
        self.retry_delay = 2

        username = os.getenv("ZKMALL_USERNAME", "18929343717")
        logger.info(
            f"API客户端初始化成功 - Base URL: {self.base_url}, Username: {username}"
        )

    def _get_headers(self) -> Dict[str, str]:
        """
        获取请求头

        Returns:
            请求头字典
        """
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "YunShang-System/1.0",
        }

        # 获取token
        token = AuthManager.get_token()
        if token:
            headers["Authorization"] = token

        return headers

    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        处理响应

        Args:
            response: HTTP响应对象

        Returns:
            解析后的JSON数据
        """
        try:
            # 记录响应基本信息
            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            # 检查HTTP状态码
            if response.status_code == 401:
                logger.warning("HTTP 401: 认证失败")
                AuthManager.logout()
                return {"code": 401, "message": "认证失败，请重新登录"}
            elif response.status_code == 403:
                logger.warning("HTTP 403: 权限不足")
                return {"code": 403, "message": "权限不足"}
            elif response.status_code == 404:
                logger.warning("HTTP 404: 资源不存在")
                return {"code": 404, "message": "请求的资源不存在"}
            elif response.status_code == 429:
                logger.warning("HTTP 429: 请求过于频繁")
                return {"code": 429, "message": "请求过于频繁，请稍后再试"}
            elif response.status_code >= 500:
                logger.error(f"HTTP {response.status_code}: 服务器错误")
                return {"code": response.status_code, "message": "服务器内部错误"}

            # 尝试解析JSON
            try:
                data = response.json()
            except ValueError as e:
                # 如果不是JSON格式，检查是否是HTML错误页面
                content_type = response.headers.get("content-type", "").lower()
                if "text/html" in content_type:
                    logger.error(f"收到HTML响应而非JSON: {response.text[:200]}...")
                    return {
                        "code": -1,
                        "message": "服务器返回了HTML页面，可能是错误页面",
                    }
                else:
                    logger.error(
                        f"JSON解析错误: {e}, 响应内容: {response.text[:200]}..."
                    )
                    return {"code": -1, "message": "响应格式错误"}

            # 检查业务状态码
            if isinstance(data, dict):
                business_code = data.get("code")
                if business_code == 401:
                    logger.warning("业务状态码401，token可能已过期")
                    AuthManager.logout()
                    return {"code": 401, "message": "认证已过期，请重新登录"}
                elif business_code != 200 and business_code is not None:
                    message = data.get("message", data.get("msg", "未知错误"))
                    logger.warning(f"业务错误: code={business_code}, message={message}")
                    return {"code": business_code, "message": message}

            return data

        except requests.exceptions.HTTPError as e:
            logger.error(f"HTTP错误: {e}")
            return {"code": response.status_code, "message": f"HTTP错误: {e}"}
        except Exception as e:
            logger.error(f"响应处理异常: {e}")
            return {"code": -1, "message": f"响应处理异常: {e}"}

    def _make_request_with_retry(
        self, method: str, endpoint: str, **kwargs
    ) -> Dict[str, Any]:
        """
        带重试的请求方法

        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数

        Returns:
            响应数据
        """
        url = f"{self.base_url}{endpoint}"

        for attempt in range(self.max_retries):
            try:
                # 确保认证
                if not AuthManager.ensure_authenticated():
                    logger.error("无法获得有效认证")
                    return {"code": 401, "message": "认证失败"}

                # 获取最新的请求头
                headers = self._get_headers()

                # 发送请求
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    timeout=self.timeout,
                    **kwargs,
                )

                result = self._handle_response(response)

                # 如果是认证错误且还有重试次数，尝试重新认证
                if result.get("code") == 401 and attempt < self.max_retries - 1:
                    logger.warning(
                        f"认证失败，尝试重新登录 (尝试 {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(self.retry_delay)
                    continue

                return result

            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    logger.error("请求达到最大重试次数")
                    return {"code": -1, "message": f"请求失败: {e}"}
                time.sleep(self.retry_delay * (2**attempt))  # 指数退避

            except Exception as e:
                logger.error(f"请求异常: {e}")
                return {"code": -1, "message": str(e)}

        return {"code": -1, "message": "未知错误"}

    def get(
        self, endpoint: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        发送GET请求

        Args:
            endpoint: API端点
            params: 查询参数

        Returns:
            响应数据
        """
        return self._make_request_with_retry("GET", endpoint, params=params)

    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送POST请求

        Args:
            endpoint: API端点
            data: 请求数据

        Returns:
            响应数据
        """
        return self._make_request_with_retry("POST", endpoint, json=data)

    # 产品相关API
    @cache_query("api_products", ttl=1800)  # 缓存30分钟
    def get_products(self, **kwargs) -> List[Dict[str, Any]]:
        """
        获取产品列表

        Args:
            **kwargs: 查询参数

        Returns:
            标准化后的产品列表
        """
        endpoint = "/api/business/product/list"
        params = {}

        # 添加分页参数
        if "pageSize" in kwargs:
            params["pageSize"] = kwargs["pageSize"]

        if "current" in kwargs:
            params["current"] = kwargs["current"]

        # 添加搜索参数
        if "productName" in kwargs:
            params["productName"] = kwargs["productName"]

        if "categoryId" in kwargs:
            params["categoryId"] = kwargs["categoryId"]

        if "brandId" in kwargs:
            params["brandId"] = kwargs["brandId"]

        if "status" in kwargs:
            params["status"] = kwargs["status"]

        if "labelListForSelect" in kwargs:
            params["labelListForSelect"] = kwargs["labelListForSelect"]

        if "orderByColumn" in kwargs:
            params["orderByColumn"] = kwargs["orderByColumn"]

        if "isAsc" in kwargs:
            params["isAsc"] = kwargs["isAsc"]

        response = self.get(endpoint, params)

        # 记录原始响应用于调试
        logger.info(f"产品API原始响应: {response}")

        # 使用标准化工具处理响应
        return ApiResponseNormalizer.normalize_list_response(
            response, data_type="product"
        )

    @cache_query("api_product_detail", ttl=3600)  # 缓存1小时
    def get_product_detail(self, product_id: int) -> Optional[Dict[str, Any]]:
        """
        获取产品详情

        Args:
            product_id: 产品ID

        Returns:
            标准化后的产品详情
        """
        params = {"id": product_id}

        response = self.get("/api/business/product/getById", params)

        # 使用标准化工具处理响应
        return ApiResponseNormalizer.normalize_single_response(
            response, data_type="product"
        )

    # 案例相关API
    @cache_query("api_cases", ttl=2400)  # 缓存40分钟
    def get_cases(
        self, product_id: Optional[int] = None, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        获取案例列表

        Args:
            product_id: 产品ID
            **kwargs: 查询参数

        Returns:
            标准化后的案例列表
        """
        # 使用正确的案例端点
        endpoint = "/api/companycase/pcList"
        params = {
            "current": kwargs.get("current", 1),
            "pageSize": kwargs.get("pageSize", 20),
        }

        if product_id:
            params["productId"] = product_id

        # 添加其他查询参数
        if "search" in kwargs:
            params["search"] = kwargs["search"]
        if "status" in kwargs:
            params["status"] = kwargs["status"]

        response = self.get(endpoint, params)

        # 记录原始响应用于调试
        logger.info(f"案例API原始响应: {response}")

        # 使用标准化工具处理响应
        return ApiResponseNormalizer.normalize_list_response(response, data_type="case")

    # 方案相关API
    @cache_query("api_programmes", ttl=2400)  # 缓存40分钟
    def get_programmes(
        self, product_id: Optional[int] = None, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        获取方案列表

        Args:
            product_id: 产品ID
            **kwargs: 查询参数

        Returns:
            标准化后的方案列表
        """
        # 尝试使用与案例API类似的端点格式
        endpoint = "/api/programme/pcList"  # 修改端点，使用pcList格式
        params = {}

        # 添加分页参数
        if "current" in kwargs:
            params["current"] = kwargs["current"]
        if "pageSize" in kwargs:
            params["pageSize"] = kwargs["pageSize"]

        if product_id:
            params["productId"] = product_id

        if "category" in kwargs:
            params["category"] = kwargs["category"]

        # 记录详细的调试信息
        logger.info(f"方案API调用参数: {params}")

        response = self.get(endpoint, params)

        # 记录原始响应用于调试
        logger.info(f"方案API原始响应: {response}")

        # 尝试标准化主端点响应
        result = ApiResponseNormalizer.normalize_list_response(
            response, data_type="programme"
        )

        # 如果主端点失败，尝试原始端点
        if not result:
            logger.warning(f"方案API端点 {endpoint} 返回数据格式异常，尝试原始端点")
            logger.info("尝试使用原始端点 /api/programme/relatedList")

            # 尝试原始端点
            original_endpoint = "/api/programme/relatedList"
            original_params = {
                "isApp": kwargs.get("isApp", 1),
                "status": kwargs.get("status", "0"),
            }
            original_params.update(params)

            logger.info(f"方案API原始端点调用参数: {original_params}")
            original_response = self.get(original_endpoint, original_params)
            logger.info(f"方案API原始端点响应: {original_response}")

            # 标准化原始端点响应
            result = ApiResponseNormalizer.normalize_list_response(
                original_response, data_type="programme"
            )

            if not result:
                logger.warning(f"方案API所有端点都无法获取数据")

        return result

    # 资讯相关API
    @cache_query("api_information", ttl=1800)  # 缓存30分钟
    def get_information(
        self, product_id: Optional[int] = None, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        获取资讯列表

        Args:
            product_id: 产品ID
            **kwargs: 查询参数

        Returns:
            标准化后的资讯列表
        """
        # 尝试使用数据更多的资讯端点
        endpoint = "/api/business/info/list"
        params = {
            "current": kwargs.get("current", 1),
            "pageSize": kwargs.get("pageSize", 20),
        }

        if product_id:
            params["productId"] = product_id

        # 添加搜索和筛选参数
        if "search" in kwargs:
            params["search"] = kwargs["search"]
        if "category" in kwargs:
            params["category"] = kwargs["category"]
        if "status" in kwargs:
            params["status"] = kwargs["status"]
        if "orderByColumn" in kwargs:
            params["orderByColumn"] = kwargs["orderByColumn"]
        if "isAsc" in kwargs:
            params["isAsc"] = kwargs["isAsc"]

        response = self.get(endpoint, params)

        # 记录原始响应用于调试
        logger.info(f"资讯API原始响应: {response}")

        # 如果主端点失败，尝试备用端点
        result = ApiResponseNormalizer.normalize_list_response(
            response, data_type="information"
        )

        if not result and response.get("code") != 200:
            logger.warning(f"资讯API端点 {endpoint} 失败，尝试备用端点")
            backup_endpoint = "/api/system/information/list"
            backup_response = self.get(backup_endpoint, params)
            logger.info(f"资讯API备用端点响应: {backup_response}")
            result = ApiResponseNormalizer.normalize_list_response(
                backup_response, data_type="information"
            )

        return result

    # 配单相关API
    def get_distribution_orders(
        self, product_id: Optional[int] = None, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        获取配单列表

        Args:
            product_id: 产品ID
            **kwargs: 查询参数

        Returns:
            标准化后的配单列表
        """
        endpoint = "/api/distributionOrderPlatform/list"
        params = {}

        # 添加分页参数
        if "current" in kwargs:
            params["current"] = kwargs["current"]
        if "pageSize" in kwargs:
            params["pageSize"] = kwargs["pageSize"]

        if product_id:
            params["productId"] = product_id

        # 记录详细的调试信息
        logger.info(f"配单API调用参数: {params}")

        response = self.get(endpoint, params)

        # 记录原始响应用于调试
        logger.info(f"配单API原始响应: {response}")

        # 使用标准化工具处理响应
        return ApiResponseNormalizer.normalize_list_response(
            response, data_type="distribution_order"
        )

    # 分类和标签相关API
    @cache_query("api_categories", ttl=7200)  # 缓存2小时，分类变化较少
    def get_categories(self) -> List[Dict[str, Any]]:
        """
        从各个业务接口中提取分类信息

        Returns:
            分类列表
        """
        try:
            logger.info("开始从各个业务接口中提取分类信息")

            all_categories = {}

            # 1. 从产品数据中提取分类信息
            try:
                logger.info("从产品数据中提取分类...")
                page = 1
                page_size = 100

                while True:
                    products = self.get_products(current=page, pageSize=page_size)
                    if not products:
                        break

                    # 从产品数据中提取分类信息
                    for product in products:
                        category_id = product.get("category")
                        category_name = product.get("categoryName")

                        if category_id and category_name:
                            all_categories[str(category_id)] = {
                                "id": int(category_id),
                                "name": category_name,
                                "source": "product",
                                "type": "product_category",
                            }

                    # 如果返回的产品数量少于页面大小，说明已经是最后一页
                    if len(products) < page_size:
                        break

                    page += 1
                    time.sleep(0.2)  # 避免请求过于频繁

                logger.info(
                    f"从产品数据中提取到 {len([c for c in all_categories.values() if c['source'] == 'product'])} 个分类"
                )
            except Exception as e:
                logger.warning(f"从产品数据提取分类失败: {e}")

            # 2. 从方案数据中提取分类信息
            try:
                logger.info("从方案数据中提取分类...")
                programmes = self.get_programmes()
                for programme in programmes:
                    category_id = programme.get("category")
                    category_name = programme.get("categoryName")

                    if category_id and category_name:
                        all_categories[f"programme_{category_id}"] = {
                            "id": int(category_id),
                            "name": category_name,
                            "source": "programme",
                            "type": "programme_category",
                        }

                logger.info(
                    f"从方案数据中提取到 {len([c for c in all_categories.values() if c['source'] == 'programme'])} 个分类"
                )
            except Exception as e:
                logger.warning(f"从方案数据提取分类失败: {e}")

            # 3. 从案例数据中提取分类信息
            try:
                logger.info("从案例数据中提取分类...")
                cases = self.get_cases()
                for case in cases:
                    category_id = case.get("category")
                    category_name = case.get("categoryName")

                    if category_id and category_name:
                        all_categories[f"case_{category_id}"] = {
                            "id": int(category_id),
                            "name": category_name,
                            "source": "case",
                            "type": "case_category",
                        }

                logger.info(
                    f"从案例数据中提取到 {len([c for c in all_categories.values() if c['source'] == 'case'])} 个分类"
                )
            except Exception as e:
                logger.warning(f"从案例数据提取分类失败: {e}")

            # 4. 从资讯数据中提取分类信息
            try:
                logger.info("从资讯数据中提取分类...")
                information = self.get_information()
                for info in information:
                    category_id = info.get("categoryId")
                    category_name = info.get("categoryName")

                    if category_id and category_name:
                        all_categories[f"info_{category_id}"] = {
                            "id": int(category_id),
                            "name": category_name,
                            "source": "information",
                            "type": "information_category",
                        }

                logger.info(
                    f"从资讯数据中提取到 {len([c for c in all_categories.values() if c['source'] == 'information'])} 个分类"
                )
            except Exception as e:
                logger.warning(f"从资讯数据提取分类失败: {e}")

            # 5. 从配单数据中提取分类信息
            try:
                logger.info("从配单数据中提取分类...")
                distribution_orders = self.get_distribution_orders()
                for order in distribution_orders:
                    # 一级分类
                    fir_category_id = order.get("firCategoryId")
                    fir_category_name = order.get("firCategoryName")
                    if fir_category_id and fir_category_name:
                        all_categories[f"dist_fir_{fir_category_id}"] = {
                            "id": int(fir_category_id),
                            "name": fir_category_name,
                            "source": "distribution_order",
                            "type": "distribution_category",
                            "level": 1,
                        }

                    # 二级分类
                    sec_category_id = order.get("secCategoryId")
                    sec_category_name = order.get("secCategoryName")
                    if sec_category_id and sec_category_name:
                        all_categories[f"dist_sec_{sec_category_id}"] = {
                            "id": int(sec_category_id),
                            "name": sec_category_name,
                            "source": "distribution_order",
                            "type": "distribution_category",
                            "level": 2,
                            "parent_id": fir_category_id,
                        }

                logger.info(
                    f"从配单数据中提取到 {len([c for c in all_categories.values() if c['source'] == 'distribution_order'])} 个分类"
                )
            except Exception as e:
                logger.warning(f"从配单数据提取分类失败: {e}")

            categories_list = list(all_categories.values())
            logger.info(f"总共成功提取 {len(categories_list)} 个分类")

            return categories_list

        except Exception as e:
            logger.error(f"提取分类信息失败: {e}")
            return []

    @cache_query("api_labels", ttl=7200)  # 缓存2小时，标签变化较少
    def get_labels(self) -> List[Dict[str, Any]]:
        """
        从产品数据中提取标签信息

        Returns:
            标签列表
        """
        try:
            logger.info("开始从产品数据中提取标签信息")

            all_labels = {}
            page = 1
            page_size = 100

            while True:
                products = self.get_products(current=page, pageSize=page_size)
                if not products:
                    break

                # 从产品数据中提取标签信息
                for product in products:
                    # 单个标签
                    label_id = product.get("label")
                    label_name = product.get("labelName")

                    if label_id and label_name:
                        all_labels[str(label_id)] = {
                            "id": int(label_id),
                            "name": label_name,
                            "status": "0",  # 默认状态
                        }

                    # 标签列表
                    label_list = product.get("labelList", [])
                    for label in label_list:
                        if isinstance(label, dict):
                            label_id = label.get("id")
                            label_name = label.get("name")

                            if label_id and label_name:
                                all_labels[str(label_id)] = {
                                    "id": int(label_id),
                                    "name": label_name,
                                    "status": label.get("status", "0"),
                                }

                # 如果返回的产品数量少于页面大小，说明已经是最后一页
                if len(products) < page_size:
                    break

                page += 1
                time.sleep(0.2)  # 避免请求过于频繁

            labels_list = list(all_labels.values())
            logger.info(f"成功提取 {len(labels_list)} 个标签")

            return labels_list

        except Exception as e:
            logger.error(f"提取标签信息失败: {e}")
            return []

    def get_pagination_info(self, response: Any) -> Dict[str, Any]:
        """
        获取分页信息

        Args:
            response: API响应

        Returns:
            标准化的分页信息
        """
        return ApiResponseNormalizer.normalize_pagination_response(response)

    def get_products_with_count(self, **kwargs) -> Dict[str, Any]:
        """
        获取产品列表及分页信息

        Args:
            **kwargs: 查询参数

        Returns:
            包含产品列表和分页信息的字典
        """
        endpoint = "/api/business/product/list"
        params = {}

        # 添加分页参数
        if "pageSize" in kwargs:
            params["pageSize"] = kwargs["pageSize"]

        if "current" in kwargs:
            params["current"] = kwargs["current"]

        # 添加搜索参数
        if "productName" in kwargs:
            params["productName"] = kwargs["productName"]

        if "categoryId" in kwargs:
            params["categoryId"] = kwargs["categoryId"]

        if "brandId" in kwargs:
            params["brandId"] = kwargs["brandId"]

        if "status" in kwargs:
            params["status"] = kwargs["status"]

        if "labelListForSelect" in kwargs:
            params["labelListForSelect"] = kwargs["labelListForSelect"]

        if "orderByColumn" in kwargs:
            params["orderByColumn"] = kwargs["orderByColumn"]

        if "isAsc" in kwargs:
            params["isAsc"] = kwargs["isAsc"]

        response = self.get(endpoint, params)

        # 使用标准化工具处理响应
        products = ApiResponseNormalizer.normalize_list_response(
            response, data_type="product"
        )
        pagination = ApiResponseNormalizer.normalize_pagination_response(response)

        return {
            "products": products,
            "pagination": pagination,
            "total": pagination.get("total", 0),
        }

    @cache_query("api_brands", ttl=7200)  # 缓存2小时，品牌变化较少
    def get_brands(self) -> List[Dict[str, Any]]:
        """
        从产品数据中提取品牌信息

        Returns:
            品牌列表
        """
        try:
            logger.info("开始从产品数据中提取品牌信息")

            all_brands = {}
            page = 1
            page_size = 100

            while True:
                products = self.get_products(current=page, pageSize=page_size)
                if not products:
                    break

                # 从产品数据中提取品牌信息
                for product in products:
                    brand_id = product.get("brandId")
                    brand_name = product.get("brandName")

                    if brand_id and brand_name:
                        all_brands[str(brand_id)] = {
                            "id": int(brand_id),
                            "name": brand_name,
                            "status": "0",  # 默认状态
                        }

                # 如果返回的产品数量少于页面大小，说明已经是最后一页
                if len(products) < page_size:
                    break

                page += 1
                time.sleep(0.2)  # 避免请求过于频繁

            brands_list = list(all_brands.values())
            logger.info(f"成功提取 {len(brands_list)} 个品牌")

            return brands_list

        except Exception as e:
            logger.error(f"提取品牌信息失败: {e}")
            return []

    def get_related_solutions(self, product_id: int) -> List[Dict[str, Any]]:
        """
        获取产品关联方案

        Args:
            product_id: 产品ID

        Returns:
            关联方案列表
        """
        try:
            # 构建查询参数
            params = {
                "productId": product_id,
                "pageSize": 20,
                "current": 1,
            }

            response = self._make_request_with_retry(
                "GET", "programme/list", params=params
            )

            if response.get("success") or response.get("code") == 200:
                data = response.get("data", {})
                programmes = data.get("rows", []) if isinstance(data, dict) else data

                logger.info(
                    f"成功获取产品 {product_id} 的关联方案: {len(programmes) if isinstance(programmes, list) else 0} 个"
                )
                return programmes if isinstance(programmes, list) else []
            else:
                logger.warning(f"获取产品 {product_id} 关联方案失败: {response}")
                return []

        except Exception as e:
            logger.error(f"获取产品 {product_id} 关联方案时发生错误: {str(e)}")
            return []

    def get_related_information(self, product_id: int) -> List[Dict[str, Any]]:
        """
        获取产品关联资讯

        Args:
            product_id: 产品ID

        Returns:
            关联资讯列表
        """
        try:
            # 构建查询参数
            params = {
                "productId": product_id,
                "pageSize": 20,
                "current": 1,
            }

            response = self._make_request_with_retry("GET", "news/list", params=params)

            if response.get("success") or response.get("code") == 200:
                data = response.get("data", {})
                news_list = data.get("rows", []) if isinstance(data, dict) else data

                logger.info(
                    f"成功获取产品 {product_id} 的关联资讯: {len(news_list) if isinstance(news_list, list) else 0} 个"
                )
                return news_list if isinstance(news_list, list) else []
            else:
                logger.warning(f"获取产品 {product_id} 关联资讯失败: {response}")
                return []

        except Exception as e:
            logger.error(f"获取产品 {product_id} 关联资讯时发生错误: {str(e)}")
            return []

    def get_related_distribution_orders(self, product_id: int) -> List[Dict[str, Any]]:
        """
        获取产品关联配单

        Args:
            product_id: 产品ID

        Returns:
            关联配单列表
        """
        try:
            # 构建查询参数
            params = {
                "productId": product_id,
                "pageSize": 20,
                "current": 1,
            }

            response = self._make_request_with_retry(
                "GET", "distributionOrderPlatform/list", params=params
            )

            if response.get("success") or response.get("code") == 200:
                data = response.get("data", {})
                orders = data.get("rows", []) if isinstance(data, dict) else data

                logger.info(
                    f"成功获取产品 {product_id} 的关联配单: {len(orders) if isinstance(orders, list) else 0} 个"
                )
                return orders if isinstance(orders, list) else []
            else:
                logger.warning(f"获取产品 {product_id} 关联配单失败: {response}")
                return []

        except Exception as e:
            logger.error(f"获取产品 {product_id} 关联配单时发生错误: {str(e)}")
            return []


# 全局API客户端实例
_api_client = None


def get_api_client() -> ZKMallClient:
    """
    获取全局API客户端实例（单例模式）

    Returns:
        ZKMallClient: API客户端实例
    """
    global _api_client
    if _api_client is None:
        _api_client = ZKMallClient()
        logger.info("创建新的API客户端实例")
    return _api_client


def reset_api_client():
    """重置API客户端实例"""
    global _api_client
    _api_client = None
    logger.info("API客户端实例已重置")
