# PowerShell script for setting environment variables
# Security-enhanced version - NO hardcoded credentials

Write-Host "Setting up environment variables for YunShang System..." -ForegroundColor Green

# Database Configuration
$env:DATABASE_HOST = "***********"
$env:DATABASE_PORT = "5432"
$env:DATABASE_NAME = "product"
$env:DATABASE_USER = "username"

# SECURITY WARNING: Set these sensitive variables manually or use .env file
# $env:DATABASE_PASSWORD = "your_secure_password_here"

# API Configuration
$env:ZKMALL_API_BASE = "https://zkmall.zktecoiot.com"

# SECURITY WARNING: Set these sensitive variables manually or use .env file
# $env:ZKMALL_USERNAME = "your_username_here"
# $env:ZKMALL_PASSWORD = "your_secure_password_here"

# Application Configuration
$env:LOG_LEVEL = "INFO"
$env:DEBUG = "False"
$env:CACHE_TTL = "3600"

Write-Host ""
Write-Host "SECURITY NOTICE:" -ForegroundColor Yellow
Write-Host "Please set the following environment variables manually:" -ForegroundColor Yellow
Write-Host "- DATABASE_PASSWORD" -ForegroundColor Red
Write-Host "- ZKMALL_USERNAME" -ForegroundColor Red
Write-Host "- ZKMALL_PASSWORD" -ForegroundColor Red
Write-Host ""
Write-Host "Or create a .env file with these values." -ForegroundColor Yellow
Write-Host ""

Read-Host "Press Enter to continue"
