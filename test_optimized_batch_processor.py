#!/usr/bin/env python3
"""
测试优化的批量处理器

验证优化后的批量处理器的功能和性能
"""

import time
from datetime import datetime
from typing import List, Dict, Any

from services.optimized_batch_processor import get_batch_processor, BatchProcessingConfig
from utils.logging_config import get_logger

logger = get_logger()


def generate_test_products(count: int, prefix: str = "optimized_test") -> List[Dict[str, Any]]:
    """生成测试产品数据"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
    products = []
    
    for i in range(count):
        products.append({
            "productId": f"{prefix}_{timestamp}_{i}",
            "productName": f"优化测试产品 {prefix} {i}",
            "brandName": "优化测试品牌",
            "categoryName": "优化测试分类",
            "price": float(i * 10 + 100),
            "paramInfoList": [
                {"params": "型号", "content": f"OPT-{i:03d}"},
                {"params": "规格", "content": "优化测试规格"},
                {"params": "颜色", "content": "测试颜色"},
                {"params": "材质", "content": "测试材质"}
            ]
        })
    
    return products


def progress_callback(processed: int, total: int):
    """进度回调函数"""
    percentage = (processed / total) * 100 if total > 0 else 0
    print(f"进度: {processed}/{total} ({percentage:.1f}%)")


def test_small_batch_processing():
    """测试小批量处理"""
    print("\n" + "="*60)
    print("🧪 测试小批量处理 (50条记录)")
    print("="*60)
    
    processor = get_batch_processor()
    test_products = generate_test_products(50, "small_batch")
    
    start_time = time.time()
    result = processor.process_products_batch(test_products, progress_callback)
    end_time = time.time()
    
    print(f"\n📊 小批量处理结果:")
    print(f"  总记录数: {result.total_items}")
    print(f"  成功处理: {result.processed_items}")
    print(f"  失败记录: {result.failed_items}")
    print(f"  处理时间: {result.processing_time:.2f}秒")
    print(f"  吞吐量: {result.throughput:.2f} 记录/秒")
    print(f"  错误数量: {len(result.errors)}")
    
    if result.errors:
        print(f"  错误详情: {result.errors[:3]}...")  # 只显示前3个错误
    
    return result.processed_items == result.total_items


def test_medium_batch_processing():
    """测试中等批量处理"""
    print("\n" + "="*60)
    print("🧪 测试中等批量处理 (250条记录)")
    print("="*60)
    
    processor = get_batch_processor()
    test_products = generate_test_products(250, "medium_batch")
    
    start_time = time.time()
    result = processor.process_products_batch(test_products, progress_callback)
    end_time = time.time()
    
    print(f"\n📊 中等批量处理结果:")
    print(f"  总记录数: {result.total_items}")
    print(f"  成功处理: {result.processed_items}")
    print(f"  失败记录: {result.failed_items}")
    print(f"  处理时间: {result.processing_time:.2f}秒")
    print(f"  吞吐量: {result.throughput:.2f} 记录/秒")
    print(f"  批次数量: {len(result.batch_results)}")
    
    # 分析批次结果
    successful_batches = [b for b in result.batch_results if not b.get("failed", False)]
    print(f"  成功批次: {len(successful_batches)}/{len(result.batch_results)}")
    
    return result.processed_items >= result.total_items * 0.95  # 95%成功率


def test_large_batch_processing():
    """测试大批量处理"""
    print("\n" + "="*60)
    print("🧪 测试大批量处理 (500条记录)")
    print("="*60)
    
    processor = get_batch_processor()
    test_products = generate_test_products(500, "large_batch")
    
    start_time = time.time()
    result = processor.process_products_batch(test_products, progress_callback)
    end_time = time.time()
    
    print(f"\n📊 大批量处理结果:")
    print(f"  总记录数: {result.total_items}")
    print(f"  成功处理: {result.processed_items}")
    print(f"  失败记录: {result.failed_items}")
    print(f"  处理时间: {result.processing_time:.2f}秒")
    print(f"  吞吐量: {result.throughput:.2f} 记录/秒")
    print(f"  批次数量: {len(result.batch_results)}")
    
    # 分析批次结果
    successful_batches = [b for b in result.batch_results if not b.get("failed", False)]
    print(f"  成功批次: {len(successful_batches)}/{len(result.batch_results)}")
    
    return result.processed_items >= result.total_items * 0.95  # 95%成功率


def test_performance_monitoring():
    """测试性能监控"""
    print("\n" + "="*60)
    print("🧪 测试性能监控")
    print("="*60)
    
    processor = get_batch_processor()
    
    # 重置统计
    processor.reset_performance_stats()
    
    # 处理几个小批次
    for i in range(3):
        test_products = generate_test_products(30, f"perf_test_{i}")
        result = processor.process_products_batch(test_products)
        print(f"  批次 {i+1} 完成: {result.processed_items} 记录")
    
    # 获取性能统计
    stats = processor.get_performance_stats()
    print(f"\n📈 性能统计:")
    print(f"  总处理记录: {stats['total_processed']}")
    print(f"  总错误数: {stats['total_errors']}")
    print(f"  平均吞吐量: {stats['avg_throughput']:.2f} 记录/秒")
    print(f"  最后处理时间: {stats['last_processing_time']:.2f}秒")
    
    return stats['total_processed'] > 0


def test_health_check():
    """测试健康检查"""
    print("\n" + "="*60)
    print("🧪 测试健康检查")
    print("="*60)
    
    processor = get_batch_processor()
    health = processor.health_check()
    
    print(f"📋 健康状态: {health['status']}")
    print(f"📅 检查时间: {health['timestamp']}")
    
    for component, status in health['components'].items():
        print(f"  {component}: {status['status']}")
        if 'error' in status:
            print(f"    错误: {status['error']}")
    
    print(f"\n📊 性能指标:")
    perf = health['performance']
    print(f"  总处理记录: {perf['total_processed']}")
    print(f"  总错误数: {perf['total_errors']}")
    print(f"  平均吞吐量: {perf['avg_throughput']:.2f} 记录/秒")
    
    return health['status'] in ['healthy', 'degraded']


def test_custom_configuration():
    """测试自定义配置"""
    print("\n" + "="*60)
    print("🧪 测试自定义配置")
    print("="*60)
    
    # 创建自定义配置
    custom_config = BatchProcessingConfig(
        optimal_batch_size=50,
        max_concurrent_threads=3,
        max_retries=2,
        retry_delay=0.5
    )
    
    from services.optimized_batch_processor import OptimizedBatchProcessor
    custom_processor = OptimizedBatchProcessor(custom_config)
    
    test_products = generate_test_products(150, "custom_config")
    result = custom_processor.process_products_batch(test_products)
    
    print(f"📊 自定义配置处理结果:")
    print(f"  批量大小: {custom_config.optimal_batch_size}")
    print(f"  并发线程: {custom_config.max_concurrent_threads}")
    print(f"  总记录数: {result.total_items}")
    print(f"  成功处理: {result.processed_items}")
    print(f"  批次数量: {len(result.batch_results)}")
    print(f"  吞吐量: {result.throughput:.2f} 记录/秒")
    
    return result.processed_items > 0


def main():
    """主测试函数"""
    print("🚀 开始测试优化的批量处理器...")
    
    test_results = []
    
    # 运行所有测试
    tests = [
        ("小批量处理", test_small_batch_processing),
        ("中等批量处理", test_medium_batch_processing),
        ("大批量处理", test_large_batch_processing),
        ("性能监控", test_performance_monitoring),
        ("健康检查", test_health_check),
        ("自定义配置", test_custom_configuration)
    ]
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 执行测试: {test_name}")
            success = test_func()
            test_results.append((test_name, success))
            print(f"✅ {test_name}: {'通过' if success else '失败'}")
        except Exception as e:
            logger.error(f"测试 {test_name} 出错: {e}", exc_info=True)
            test_results.append((test_name, False))
            print(f"❌ {test_name}: 出错 - {str(e)}")
    
    # 汇总结果
    print("\n" + "="*80)
    print("📋 测试结果汇总")
    print("="*80)
    
    passed_tests = sum(1 for _, success in test_results if success)
    total_tests = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试都通过了！优化的批量处理器工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查日志了解详情。")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
