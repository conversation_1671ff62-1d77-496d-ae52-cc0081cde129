#!/usr/bin/env python3
"""
产品数据存储服务

负责将完整解析的产品数据存储到数据库
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
from utils.database import (
    DatabaseManager,
    force_cleanup_db_connections,
    reset_db_connection_pool,
)
from services.complete_product_parser import CompleteProductParser

logger = logging.getLogger(__name__)


class ProductStorageService:
    """产品数据存储服务"""

    def __init__(self):
        """初始化存储服务"""
        self.db_manager = DatabaseManager()
        self.parser = CompleteProductParser()
        self.table_name = "products_complete"
        self._ensure_table_exists()

    def _ensure_table_exists(self):
        """确保产品表存在"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS products_complete (
            id SERIAL PRIMARY KEY,
            product_id INTEGER UNIQUE,
            name TEXT,
            spec TEXT,
            introduction TEXT,
            extracted_model TEXT,
            category_id INTEGER,
            category_name TEXT,
            brand_id INTEGER,
            brand_name TEXT,
            price DECIMAL(10,2) DEFAULT 0.0,
            all_fields JSONB,
            unknown_fields JSONB,
            parsed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_products_complete_product_id ON products_complete(product_id);
        CREATE INDEX IF NOT EXISTS idx_products_complete_name ON products_complete(name);
        CREATE INDEX IF NOT EXISTS idx_products_complete_extracted_model ON products_complete(extracted_model);
        """

        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(create_table_sql)
                    conn.commit()
            logger.info("产品表创建/验证成功")
        except Exception as e:
            logger.error(f"创建产品表失败: {e}")

    def store_products_batch(
        self, raw_products: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """批量存储产品数据"""
        logger.info(f"开始批量存储 {len(raw_products)} 条产品数据")

        processed_count = 0
        error_count = 0
        updated_count = 0
        connection_errors = 0

        # 每处理一定数量的产品后检查连接池状态
        check_interval = 50

        for i, raw_product in enumerate(raw_products):
            try:
                # 定期检查连接池状态
                if i > 0 and i % check_interval == 0:
                    self._check_and_cleanup_connections()

                parsed_product = self.parser.parse_product(raw_product)
                result = self._store_single_product(parsed_product, raw_product)

                if result == "inserted":
                    processed_count += 1
                elif result == "updated":
                    updated_count += 1
                else:
                    error_count += 1

            except Exception as e:
                error_msg = str(e).lower()

                # 检查是否是连接池相关错误
                if (
                    "connection pool exhausted" in error_msg
                    or "connection" in error_msg
                ):
                    connection_errors += 1
                    logger.warning(f"检测到连接池问题，尝试恢复: {e}")

                    # 如果连接错误过多，尝试重置连接池
                    if connection_errors >= 5:
                        logger.warning("连接错误过多，重置连接池")
                        try:
                            reset_db_connection_pool()
                            connection_errors = 0  # 重置计数
                        except Exception as reset_e:
                            logger.error(f"重置连接池失败: {reset_e}")

                logger.error(f"存储产品数据失败: {e}")
                error_count += 1

        result = {
            "inserted": processed_count,
            "updated": updated_count,
            "errors": error_count,
            "total": len(raw_products),
            "connection_errors": connection_errors,
        }

        logger.info(f"批量存储完成: {result}")
        return result

    def _check_and_cleanup_connections(self):
        """检查并清理连接池"""
        try:
            # 执行连接池清理
            force_cleanup_db_connections()
            logger.debug("连接池清理完成")
        except Exception as e:
            logger.warning(f"连接池清理失败: {e}")

    def _store_single_product(
        self, parsed_product: Dict[str, Any], raw_product: Dict[str, Any]
    ) -> str:
        """存储单个产品数据"""
        product_id = None
        try:
            # 首先获取产品ID，确保在异常处理中可用
            product_id = parsed_product.get("product_id")
            if not product_id:
                logger.warning("产品ID为空，跳过存储")
                return "error"

            # 使用带降级策略的连接获取
            conn = self.db_manager.get_connection_with_fallback(
                timeout=60, max_retries=3
            )

            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    # 检查是否存在
                    cursor.execute(
                        "SELECT id FROM products_complete WHERE product_id = %s",
                        (product_id,),
                    )
                    existing = cursor.fetchone()

                    # 准备数据
                    data = {
                        "product_id": product_id,
                        "name": parsed_product.get("name", ""),
                        "spec": parsed_product.get("spec", ""),
                        "introduction": parsed_product.get("introduction", ""),
                        "extracted_model": parsed_product.get("extracted_model", ""),
                        "category_id": parsed_product.get("category_id"),
                        "category_name": parsed_product.get("category_name", ""),
                        "brand_id": parsed_product.get("brand_id"),
                        "brand_name": parsed_product.get("brand_name", ""),
                        "price": parsed_product.get("price", 0.0),
                        "all_fields": json.dumps(
                            parsed_product, ensure_ascii=False, default=str
                        ),
                        "unknown_fields": parsed_product.get("unknown_fields"),
                        "parsed_at": parsed_product.get("parsed_at", datetime.now()),
                        "updated_at": datetime.now(),
                    }

                    if existing:
                        # 更新
                        update_sql = """
                        UPDATE products_complete SET
                            name = %s, spec = %s, introduction = %s, extracted_model = %s,
                            category_id = %s, category_name = %s, brand_id = %s, brand_name = %s,
                            price = %s, all_fields = %s, unknown_fields = %s, parsed_at = %s, updated_at = %s
                        WHERE product_id = %s
                        """
                        cursor.execute(
                            update_sql,
                            (
                                data["name"],
                                data["spec"],
                                data["introduction"],
                                data["extracted_model"],
                                data["category_id"],
                                data["category_name"],
                                data["brand_id"],
                                data["brand_name"],
                                data["price"],
                                data["all_fields"],
                                data["unknown_fields"],
                                data["parsed_at"],
                                data["updated_at"],
                                product_id,
                            ),
                        )
                        conn.commit()
                        return "updated"
                    else:
                        # 插入
                        insert_sql = """
                        INSERT INTO products_complete
                        (product_id, name, spec, introduction, extracted_model, category_id, category_name,
                         brand_id, brand_name, price, all_fields, unknown_fields, parsed_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """
                        cursor.execute(
                            insert_sql,
                            (
                                data["product_id"],
                                data["name"],
                                data["spec"],
                                data["introduction"],
                                data["extracted_model"],
                                data["category_id"],
                                data["category_name"],
                                data["brand_id"],
                                data["brand_name"],
                                data["price"],
                                data["all_fields"],
                                data["unknown_fields"],
                                data["parsed_at"],
                                data["updated_at"],
                            ),
                        )
                        conn.commit()
                        return "inserted"

            finally:
                # 确保连接被正确归还
                try:
                    self.db_manager.return_connection(conn)
                except Exception as e:
                    logger.warning(f"归还连接失败: {e}")

        except Exception as e:
            # 现在product_id在这里是安全的
            product_id_str = str(product_id) if product_id else "未知"
            logger.error(f"存储产品 {product_id_str} 失败: {e}")
            return "error"

    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:

                    cursor.execute(f"SELECT COUNT(*) as total FROM {self.table_name}")
                    total = cursor.fetchone()["total"]

                    cursor.execute(
                        f"SELECT COUNT(*) as with_model FROM {self.table_name} "
                        f"WHERE extracted_model IS NOT NULL AND extracted_model != ''"
                    )
                    with_model = cursor.fetchone()["with_model"]

                    cursor.execute(
                        f"SELECT MAX(updated_at) as last_update FROM {self.table_name}"
                    )
                    last_update = cursor.fetchone()["last_update"]

                    return {
                        "total_products": total,
                        "products_with_model": with_model,
                        "last_update": last_update,
                        "model_coverage": (
                            f"{(with_model/total*100):.1f}%" if total > 0 else "0%"
                        ),
                    }

        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {}

    def get_missing_fields_report(self) -> Dict[str, Any]:
        """获取缺失字段报告"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:

                    cursor.execute(
                        f"SELECT unknown_fields FROM {self.table_name} "
                        f"WHERE unknown_fields IS NOT NULL"
                    )

                    all_unknown_fields = set()
                    for row in cursor.fetchall():
                        if row["unknown_fields"]:
                            unknown_fields = json.loads(row["unknown_fields"])
                            all_unknown_fields.update(unknown_fields.keys())

                    return {
                        "total_unknown_fields": len(all_unknown_fields),
                        "unknown_field_names": sorted(list(all_unknown_fields)),
                    }

        except Exception as e:
            logger.error(f"获取缺失字段报告失败: {e}")
            return {}
