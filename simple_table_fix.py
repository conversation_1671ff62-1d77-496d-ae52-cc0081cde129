#!/usr/bin/env python3
"""
简化的表修复脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file
load_env_file()

from utils.database import get_database_manager


def fix_table_simple():
    """简化的表修复"""
    db_manager = get_database_manager()
    
    try:
        conn = db_manager.get_connection_with_fallback()
        
        try:
            with conn.cursor() as cursor:
                print("🔧 修复产品表...")
                
                # 1. 删除现有表（如果存在）
                cursor.execute("DROP TABLE IF EXISTS products_complete")
                print("  清理现有表...")
                
                # 2. 创建新表，product_id使用VARCHAR类型
                create_sql = """
                CREATE TABLE products_complete (
                    id SERIAL PRIMARY KEY,
                    product_id VARCHAR(100) UNIQUE NOT NULL,
                    name TEXT,
                    spec TEXT,
                    introduction TEXT,
                    extracted_model TEXT,
                    category_id INTEGER,
                    category_name VARCHAR(255),
                    brand_id INTEGER,
                    brand_name VARCHAR(255),
                    price DECIMAL(10,2) DEFAULT 0.0,
                    all_fields JSONB,
                    unknown_fields JSONB,
                    parsed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
                
                cursor.execute(create_sql)
                print("  ✅ 创建新表成功")
                
                # 3. 创建索引
                cursor.execute("CREATE INDEX idx_products_complete_product_id ON products_complete(product_id)")
                cursor.execute("CREATE INDEX idx_products_complete_name ON products_complete(name)")
                print("  ✅ 创建索引成功")
                
                conn.commit()
                print("✅ 表修复完成")
                
                return True
                
        finally:
            db_manager.return_connection(conn)
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False


def test_storage():
    """测试存储功能"""
    print("\n🧪 测试产品存储...")
    
    try:
        from services.product_storage_service import ProductStorageService
        
        storage_service = ProductStorageService()
        
        # 测试数据
        test_data = {
            "productId": "test_fix_001",
            "productName": "修复测试产品",
            "brandName": "测试品牌",
            "categoryName": "测试分类",
            "price": 99.99,
            "paramInfoList": [
                {"params": "型号", "content": "FIX-001"}
            ]
        }
        
        result = storage_service.store_products_batch([test_data])
        print(f"存储结果: {result}")
        
        if result.get("errors", 0) == 0:
            print("✅ 存储测试成功")
            return True
        else:
            print("❌ 存储测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 简化表修复")
    print("=" * 30)
    
    if fix_table_simple():
        test_storage()
    else:
        print("❌ 修复失败")
        sys.exit(1)
