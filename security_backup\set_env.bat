@echo off
REM Windows batch script for setting environment variables
REM Avoid Chinese characters to prevent encoding issues

REM Database Configuration
set DATABASE_URL=***********************************************/product
set DATABASE_HOST=***********
set DATABASE_PORT=5432
set DATABASE_NAME=product
set DATABASE_USER=username
set DATABASE_PASSWORD=password

REM API Configuration
set ZKMALL_API_BASE=https://zkmall.zktecoiot.com
set ZKMALL_USERNAME=18929343717
set ZKMALL_PASSWORD=Zk@123456

REM Application Configuration
set LOG_LEVEL=INFO
set DEBUG=False
set CACHE_TTL=3600

REM Display configuration (English only to avoid encoding issues)
echo Environment variables configured successfully!
echo Database URL: %DATABASE_URL%
echo API Base: %ZKMALL_API_BASE%
echo Database Host: %DATABASE_HOST%
echo Database Port: %DATABASE_PORT%
echo Database Name: %DATABASE_NAME%
echo Database User: %DATABASE_USER%
echo Debug Mode: %DEBUG%
echo Cache TTL: %CACHE_TTL%
