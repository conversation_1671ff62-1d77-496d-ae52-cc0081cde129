@echo off
REM Windows batch script for setting environment variables
REM Security-enhanced version - NO hardcoded credentials

echo Setting up environment variables for YunShang System...

REM Database Configuration
set DATABASE_HOST=***********
set DATABASE_PORT=5432
set DATABASE_NAME=product
set DATABASE_USER=username

REM SECURITY WARNING: Set these sensitive variables manually or use .env file
REM set DATABASE_PASSWORD=your_secure_password_here

REM API Configuration
set ZKMALL_API_BASE=https://zkmall.zktecoiot.com

REM SECURITY WARNING: Set these sensitive variables manually or use .env file
REM set ZKMALL_USERNAME=your_username_here
REM set ZKMALL_PASSWORD=your_secure_password_here

REM Application Configuration
set LOG_LEVEL=INFO
set DEBUG=False
set CACHE_TTL=3600

echo.
echo SECURITY NOTICE:
echo Please set the following environment variables manually:
echo - DATABASE_PASSWORD
echo - ZKMALL_USERNAME  
echo - Z<PERSON>MALL_PASSWORD
echo.
echo Or create a .env file with these values.
echo.

pause
