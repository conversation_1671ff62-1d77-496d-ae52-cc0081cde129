#!/usr/bin/env python3
"""
批量数据处理性能优化测试

测试和优化批量数据处理的性能，包括：
1. 数据库连接池优化
2. 批量操作性能测试
3. 缓存系统集成验证
4. 内存使用优化
"""

import time
import logging
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import os

# 导入项目模块
from utils.database import DatabaseManager, get_database_manager
from services.product_storage_service import ProductStorageService
from utils.cache import CacheManager
from utils.logging_config import get_logger

# 配置日志
logger = get_logger()


class BatchProcessingOptimizer:
    """批量处理性能优化器"""
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.storage_service = ProductStorageService()
        self.cache_manager = CacheManager()
        self.performance_stats = {}
        
    def test_database_connection_pool(self) -> Dict[str, Any]:
        """测试数据库连接池性能"""
        logger.info("🔍 测试数据库连接池性能...")
        
        start_time = time.time()
        results = {
            "connection_tests": [],
            "pool_status": {},
            "performance_metrics": {}
        }
        
        # 测试连接获取速度
        for i in range(10):
            conn_start = time.time()
            try:
                with self.db_manager.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                conn_time = time.time() - conn_start
                results["connection_tests"].append({
                    "test_id": i + 1,
                    "connection_time": conn_time,
                    "success": True
                })
            except Exception as e:
                results["connection_tests"].append({
                    "test_id": i + 1,
                    "connection_time": time.time() - conn_start,
                    "success": False,
                    "error": str(e)
                })
        
        # 获取连接池状态
        try:
            results["pool_status"] = self.db_manager.get_pool_status()
        except Exception as e:
            logger.error(f"获取连接池状态失败: {e}")
            results["pool_status"] = {"error": str(e)}
        
        # 计算性能指标
        successful_tests = [t for t in results["connection_tests"] if t["success"]]
        if successful_tests:
            connection_times = [t["connection_time"] for t in successful_tests]
            results["performance_metrics"] = {
                "avg_connection_time": sum(connection_times) / len(connection_times),
                "max_connection_time": max(connection_times),
                "min_connection_time": min(connection_times),
                "success_rate": len(successful_tests) / len(results["connection_tests"]) * 100,
                "total_test_time": time.time() - start_time
            }
        
        logger.info(f"✅ 连接池测试完成: {results['performance_metrics']}")
        return results
    
    def test_batch_insert_performance(self, batch_sizes: List[int] = [10, 50, 100, 200]) -> Dict[str, Any]:
        """测试不同批量大小的插入性能"""
        logger.info("🔍 测试批量插入性能...")
        
        results = {
            "batch_tests": [],
            "optimal_batch_size": None,
            "performance_summary": {}
        }
        
        for batch_size in batch_sizes:
            logger.info(f"测试批量大小: {batch_size}")
            
            # 生成测试数据
            test_products = self._generate_test_products(batch_size)
            
            # 测试插入性能
            start_time = time.time()
            memory_before = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            try:
                result = self.storage_service.store_products_batch(test_products)
                
                end_time = time.time()
                memory_after = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                
                batch_result = {
                    "batch_size": batch_size,
                    "processing_time": end_time - start_time,
                    "memory_usage": memory_after - memory_before,
                    "throughput": batch_size / (end_time - start_time),  # 记录/秒
                    "success_rate": (result.get("inserted", 0) + result.get("updated", 0)) / batch_size * 100,
                    "storage_result": result
                }
                
                results["batch_tests"].append(batch_result)
                logger.info(f"批量大小 {batch_size}: {batch_result['throughput']:.2f} 记录/秒")
                
            except Exception as e:
                logger.error(f"批量大小 {batch_size} 测试失败: {e}")
                results["batch_tests"].append({
                    "batch_size": batch_size,
                    "error": str(e),
                    "success": False
                })
        
        # 找出最优批量大小
        successful_tests = [t for t in results["batch_tests"] if "throughput" in t]
        if successful_tests:
            optimal_test = max(successful_tests, key=lambda x: x["throughput"])
            results["optimal_batch_size"] = optimal_test["batch_size"]
            
            results["performance_summary"] = {
                "best_throughput": optimal_test["throughput"],
                "best_batch_size": optimal_test["batch_size"],
                "avg_throughput": sum(t["throughput"] for t in successful_tests) / len(successful_tests),
                "total_tests": len(successful_tests)
            }
        
        logger.info(f"✅ 最优批量大小: {results['optimal_batch_size']}")
        return results
    
    def test_cache_integration(self) -> Dict[str, Any]:
        """测试缓存系统集成"""
        logger.info("🔍 测试缓存系统集成...")
        
        results = {
            "cache_tests": [],
            "performance_metrics": {}
        }
        
        test_data = {"test_key": "test_value", "timestamp": datetime.now().isoformat()}
        
        # 测试缓存写入
        start_time = time.time()
        cache_set_success = self.cache_manager.set("test_batch_key", test_data, ttl=300)
        cache_set_time = time.time() - start_time
        
        results["cache_tests"].append({
            "operation": "set",
            "success": cache_set_success,
            "time": cache_set_time
        })
        
        # 测试缓存读取
        start_time = time.time()
        cached_data = self.cache_manager.get("test_batch_key")
        cache_get_time = time.time() - start_time
        
        results["cache_tests"].append({
            "operation": "get",
            "success": cached_data is not None,
            "time": cache_get_time,
            "data_match": cached_data == test_data if cached_data else False
        })
        
        # 测试缓存删除
        start_time = time.time()
        cache_delete_success = self.cache_manager.delete("test_batch_key")
        cache_delete_time = time.time() - start_time
        
        results["cache_tests"].append({
            "operation": "delete",
            "success": cache_delete_success,
            "time": cache_delete_time
        })
        
        # 计算性能指标
        results["performance_metrics"] = {
            "avg_operation_time": sum(t["time"] for t in results["cache_tests"]) / len(results["cache_tests"]),
            "all_operations_successful": all(t["success"] for t in results["cache_tests"]),
            "cache_system_available": any(t["success"] for t in results["cache_tests"])
        }
        
        logger.info(f"✅ 缓存系统测试完成: {results['performance_metrics']}")
        return results
    
    def test_concurrent_processing(self, num_threads: int = 5, items_per_thread: int = 20) -> Dict[str, Any]:
        """测试并发处理性能"""
        logger.info(f"🔍 测试并发处理性能 ({num_threads} 线程, 每线程 {items_per_thread} 项)...")
        
        results = {
            "thread_results": [],
            "performance_metrics": {},
            "errors": []
        }
        
        start_time = time.time()
        
        def process_batch(thread_id: int) -> Dict[str, Any]:
            """单线程处理函数"""
            thread_start = time.time()
            try:
                test_products = self._generate_test_products(items_per_thread, f"thread_{thread_id}")
                result = self.storage_service.store_products_batch(test_products)
                
                return {
                    "thread_id": thread_id,
                    "processing_time": time.time() - thread_start,
                    "items_processed": items_per_thread,
                    "storage_result": result,
                    "success": True
                }
            except Exception as e:
                return {
                    "thread_id": thread_id,
                    "processing_time": time.time() - thread_start,
                    "error": str(e),
                    "success": False
                }
        
        # 执行并发处理
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(process_batch, i) for i in range(num_threads)]
            
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results["thread_results"].append(result)
                except Exception as e:
                    results["errors"].append(str(e))
        
        total_time = time.time() - start_time
        
        # 计算性能指标
        successful_threads = [r for r in results["thread_results"] if r.get("success", False)]
        if successful_threads:
            total_items = sum(r["items_processed"] for r in successful_threads)
            results["performance_metrics"] = {
                "total_processing_time": total_time,
                "total_items_processed": total_items,
                "overall_throughput": total_items / total_time,
                "successful_threads": len(successful_threads),
                "failed_threads": len(results["thread_results"]) - len(successful_threads),
                "avg_thread_time": sum(r["processing_time"] for r in successful_threads) / len(successful_threads)
            }
        
        logger.info(f"✅ 并发处理测试完成: {results['performance_metrics']}")
        return results
    
    def _generate_test_products(self, count: int, prefix: str = "batch_test") -> List[Dict[str, Any]]:
        """生成测试产品数据"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        products = []
        
        for i in range(count):
            products.append({
                "productId": f"{prefix}_{timestamp}_{i}",
                "productName": f"批量测试产品 {prefix} {i}",
                "brandName": "批量测试品牌",
                "categoryName": "批量测试分类",
                "price": float(i * 10 + 100),
                "paramInfoList": [
                    {"params": "型号", "content": f"BATCH-{i:03d}"},
                    {"params": "规格", "content": "批量测试规格"}
                ]
            })
        
        return products
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合性能测试"""
        logger.info("🚀 开始综合批量处理性能测试...")
        
        comprehensive_results = {
            "test_timestamp": datetime.now().isoformat(),
            "system_info": {
                "cpu_count": psutil.cpu_count(),
                "memory_total": psutil.virtual_memory().total / 1024 / 1024 / 1024,  # GB
                "python_version": os.sys.version
            },
            "tests": {}
        }
        
        # 1. 数据库连接池测试
        comprehensive_results["tests"]["connection_pool"] = self.test_database_connection_pool()
        
        # 2. 批量插入性能测试
        comprehensive_results["tests"]["batch_insert"] = self.test_batch_insert_performance()
        
        # 3. 缓存系统测试
        comprehensive_results["tests"]["cache_integration"] = self.test_cache_integration()
        
        # 4. 并发处理测试
        comprehensive_results["tests"]["concurrent_processing"] = self.test_concurrent_processing()
        
        # 5. 生成优化建议
        comprehensive_results["optimization_recommendations"] = self._generate_optimization_recommendations(
            comprehensive_results["tests"]
        )
        
        logger.info("✅ 综合性能测试完成")
        return comprehensive_results
    
    def _generate_optimization_recommendations(self, test_results: Dict[str, Any]) -> List[str]:
        """基于测试结果生成优化建议"""
        recommendations = []
        
        # 连接池优化建议
        if "connection_pool" in test_results:
            pool_metrics = test_results["connection_pool"].get("performance_metrics", {})
            avg_conn_time = pool_metrics.get("avg_connection_time", 0)
            
            if avg_conn_time > 0.1:  # 超过100ms
                recommendations.append("建议优化数据库连接池配置，连接获取时间过长")
            
            success_rate = pool_metrics.get("success_rate", 100)
            if success_rate < 95:
                recommendations.append("数据库连接成功率偏低，建议检查连接池配置和网络状况")
        
        # 批量处理优化建议
        if "batch_insert" in test_results:
            optimal_size = test_results["batch_insert"].get("optimal_batch_size")
            if optimal_size:
                recommendations.append(f"建议使用批量大小 {optimal_size} 以获得最佳性能")
        
        # 缓存系统建议
        if "cache_integration" in test_results:
            cache_available = test_results["cache_integration"]["performance_metrics"].get("cache_system_available", False)
            if not cache_available:
                recommendations.append("缓存系统不可用，建议检查Redis连接或使用内存缓存")
        
        # 并发处理建议
        if "concurrent_processing" in test_results:
            concurrent_metrics = test_results["concurrent_processing"].get("performance_metrics", {})
            failed_threads = concurrent_metrics.get("failed_threads", 0)
            if failed_threads > 0:
                recommendations.append("并发处理存在失败线程，建议检查资源限制和错误处理")
        
        if not recommendations:
            recommendations.append("系统性能良好，无需特别优化")
        
        return recommendations


def main():
    """主函数"""
    print("🚀 启动批量数据处理性能优化测试...")
    
    try:
        optimizer = BatchProcessingOptimizer()
        results = optimizer.run_comprehensive_test()
        
        print("\n" + "="*80)
        print("📊 测试结果摘要")
        print("="*80)
        
        # 打印系统信息
        system_info = results["system_info"]
        print(f"CPU核心数: {system_info['cpu_count']}")
        print(f"内存总量: {system_info['memory_total']:.2f} GB")
        
        # 打印各项测试结果
        for test_name, test_result in results["tests"].items():
            print(f"\n📋 {test_name.upper()} 测试:")
            if "performance_metrics" in test_result:
                for key, value in test_result["performance_metrics"].items():
                    print(f"  {key}: {value}")
        
        # 打印优化建议
        print(f"\n💡 优化建议:")
        for i, recommendation in enumerate(results["optimization_recommendations"], 1):
            print(f"  {i}. {recommendation}")
        
        print("\n✅ 批量处理性能测试完成!")
        return True
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    main()
