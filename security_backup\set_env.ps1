# PowerShell script for setting environment variables
# Windows PowerShell version of set_env.bat

Write-Host "Setting up environment variables for YunShang System..." -ForegroundColor Green

# Database Configuration
$env:DATABASE_URL = "***********************************************/product"
$env:DATABASE_HOST = "***********"
$env:DATABASE_PORT = "5432"
$env:DATABASE_NAME = "product"
$env:DATABASE_USER = "username"
$env:DATABASE_PASSWORD = "password"

# API Configuration
$env:ZKMALL_API_BASE = "https://zkmall.zktecoiot.com"
$env:ZKMALL_USERNAME = "your_username_here"  # 请替换为实际用户名
$env:ZKMALL_PASSWORD = "your_password_here"  # 请替换为实际密码

# Application Configuration
$env:LOG_LEVEL = "INFO"
$env:DEBUG = "False"
$env:CACHE_TTL = "3600"

# FastGPT配置
$env:FASTGPT_API_BASE = "http://**************:3000/api"
$env:FASTGPT_API_KEY = "fastgpt-sS63QySGONaqCs3kdiGfxT4PfQKYGLC59kejABMsNPgIbNnamHNnWwQmj"
$env:FASTGPT_TIMEOUT = "30"
$env:FASTGPT_MAX_RETRIES = "3"

# 产品知识库配置 - 根据产品类别配置不同的知识库ID
# 门禁类产品 - 使用提供的知识库ID
$env:FASTGPT_ACCESS_CONTROL_DATASET_ID = "6855376579aa612c160b9d8a"
$env:FASTGPT_ATTENDANCE_DATASET_ID = "6855376579aa612c160b9d8a"
$env:FASTGPT_FACE_RECOGNITION_DATASET_ID = "6855376579aa612c160b9d8a"

# 默认分类 - 使用门禁知识库作为默认
$env:FASTGPT_DEFAULT_PRODUCT_DATASET_ID = "6855376579aa612c160b9d8a"



# Display configuration
Write-Host "Environment variables configured successfully!" -ForegroundColor Green
Write-Host "Database URL: $env:DATABASE_URL" -ForegroundColor Cyan
Write-Host "API Base: $env:ZKMALL_API_BASE" -ForegroundColor Cyan
Write-Host "Database Host: $env:DATABASE_HOST" -ForegroundColor Cyan
Write-Host "Database Port: $env:DATABASE_PORT" -ForegroundColor Cyan
Write-Host "Database Name: $env:DATABASE_NAME" -ForegroundColor Cyan
Write-Host "Database User: $env:DATABASE_USER" -ForegroundColor Cyan
Write-Host "Debug Mode: $env:DEBUG" -ForegroundColor Cyan
Write-Host "Cache TTL: $env:CACHE_TTL" -ForegroundColor Cyan
Write-Host "FastGPT API Base: $env:FASTGPT_API_BASE" -ForegroundColor Cyan
Write-Host "FastGPT Dataset ID: $env:FASTGPT_ACCESS_CONTROL_DATASET_ID" -ForegroundColor Cyan

Write-Host "Environment setup completed!" -ForegroundColor Green 