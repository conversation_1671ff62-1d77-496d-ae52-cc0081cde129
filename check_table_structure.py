#!/usr/bin/env python3
"""
检查数据库表结构脚本
"""

from utils.database import get_database_manager
from config import load_env_file

# 加载环境变量
load_env_file()

def check_table_structure():
    """检查products_complete表结构"""
    db_manager = get_database_manager()
    
    try:
        conn = db_manager.get_connection_with_fallback()
        
        try:
            with conn.cursor() as cursor:
                # 检查表是否存在
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'products_complete'
                    )
                """)
                table_exists = cursor.fetchone()[0]
                
                if not table_exists:
                    print("❌ products_complete 表不存在")
                    return False
                
                print("✅ products_complete 表存在")
                
                # 获取表结构
                cursor.execute("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = 'products_complete'
                    ORDER BY ordinal_position
                """)
                
                columns = cursor.fetchall()
                print(f"\n📋 products_complete 表结构 ({len(columns)} 个字段):")
                print("-" * 80)
                
                for col in columns:
                    nullable = "NULL" if col[2] == "YES" else "NOT NULL"
                    default = f"DEFAULT {col[3]}" if col[3] else ""
                    print(f"  {col[0]:<20} {col[1]:<15} {nullable:<8} {default}")
                
                # 检查关键字段
                column_names = [col[0] for col in columns]
                
                print(f"\n🔍 关键字段检查:")
                required_fields = [
                    'product_id', 'name', 'spec', 'introduction', 
                    'extracted_model', 'category_id', 'category_name',
                    'brand_id', 'brand_name', 'price'
                ]
                
                for field in required_fields:
                    if field in column_names:
                        print(f"  ✅ {field}")
                    else:
                        print(f"  ❌ {field} - 缺失")
                
                # 检查product_id字段类型
                product_id_info = next((col for col in columns if col[0] == 'product_id'), None)
                if product_id_info:
                    print(f"\n📊 product_id 字段详情:")
                    print(f"  类型: {product_id_info[1]}")
                    print(f"  可空: {product_id_info[2]}")
                    print(f"  默认值: {product_id_info[3] or 'None'}")
                
                return True
                
        finally:
            db_manager.return_connection(conn)
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False


def fix_table_structure():
    """修复表结构问题"""
    db_manager = get_database_manager()
    
    try:
        conn = db_manager.get_connection_with_fallback()
        
        try:
            with conn.cursor() as cursor:
                print("\n🔧 开始修复表结构...")
                
                # 1. 检查并添加 extracted_model 字段
                cursor.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'products_complete' AND column_name = 'extracted_model'
                """)
                
                if not cursor.fetchone():
                    print("  添加 extracted_model 字段...")
                    cursor.execute("""
                        ALTER TABLE products_complete 
                        ADD COLUMN extracted_model TEXT
                    """)
                    print("  ✅ extracted_model 字段已添加")
                else:
                    print("  ✅ extracted_model 字段已存在")
                
                # 2. 检查 product_id 字段类型
                cursor.execute("""
                    SELECT data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'products_complete' AND column_name = 'product_id'
                """)
                
                result = cursor.fetchone()
                if result:
                    current_type = result[0]
                    print(f"  当前 product_id 类型: {current_type}")
                    
                    if current_type == 'integer':
                        print("  需要将 product_id 改为 VARCHAR 类型以支持字符串ID...")
                        
                        # 修改字段类型
                        cursor.execute("""
                            ALTER TABLE products_complete 
                            ALTER COLUMN product_id TYPE VARCHAR(100)
                        """)
                        print("  ✅ product_id 类型已改为 VARCHAR(100)")
                    else:
                        print(f"  ✅ product_id 类型已是 {current_type}")
                
                conn.commit()
                print("\n🎉 表结构修复完成!")
                return True
                
        finally:
            db_manager.return_connection(conn)
            
    except Exception as e:
        print(f"❌ 修复表结构失败: {e}")
        return False


if __name__ == "__main__":
    print("🔍 检查数据库表结构")
    print("=" * 50)
    
    # 检查表结构
    if check_table_structure():
        # 修复表结构
        fix_table_structure()
        
        # 再次检查
        print("\n" + "=" * 50)
        print("🔍 修复后的表结构")
        check_table_structure()
    else:
        print("❌ 无法检查表结构，请检查数据库连接")
